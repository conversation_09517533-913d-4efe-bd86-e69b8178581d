package middleware

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"iaa-gamelog/internal/config"
	"iaa-gamelog/internal/models"

	"github.com/gin-gonic/gin"
)

// calculateSign 计算MD5签名
func calculateSign(params map[string]string, secret string) string {
	// 参数按字母排序
	keys := make([]string, 0, len(params))
	for k := range params {
		if k != "sign" { // 排除sign参数本身
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 拼接参数
	var paramStr strings.Builder
	for i, key := range keys {
		if i > 0 {
			paramStr.WriteString("&")
		}
		paramStr.WriteString(key)
		paramStr.WriteString("=")
		paramStr.WriteString(params[key])
	}

	// 添加密钥
	paramStr.WriteString(secret)

	// 计算MD5
	hash := md5.Sum([]byte(paramStr.String()))
	return hex.EncodeToString(hash[:])
}

// VerifySignature 验证签名中间件
func VerifySignature(appConfig *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取AppSecret配置
		appSecret := appConfig.Security.AppSecret
		if appSecret == "" {
			c.JSON(http.StatusInternalServerError, models.StandardResponse{
				Code: -1,
				Msg:  "服务器配置错误",
			})
			c.Abort()
			return
		}

		// 解析请求参数
		var params map[string]string
		if c.Request.Method == "GET" {
			// GET请求从查询参数获取
			params = make(map[string]string)
			for key, values := range c.Request.URL.Query() {
				if len(values) > 0 {
					params[key] = values[0]
				}
			}
		} else {
			// POST请求从表单或JSON获取
			params = make(map[string]string)

			// 尝试解析表单数据
			if err := c.Request.ParseForm(); err == nil {
				for key, values := range c.Request.PostForm {
					if len(values) > 0 {
						params[key] = values[0]
					}
				}
			}

			// 如果表单为空，尝试解析JSON（不消费请求体）
			if len(params) == 0 {
				// 读取请求体但不消费
				bodyBytes, err := io.ReadAll(c.Request.Body)
				if err == nil {
					// 重新设置请求体，以便后续处理器可以读取
					c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

					var jsonParams map[string]interface{}
					if err := json.Unmarshal(bodyBytes, &jsonParams); err == nil {
						for key, value := range jsonParams {
							// 特殊处理数字类型，避免科学计数法
							switch v := value.(type) {
							case float64:
								// 检查是否为整数
								if v == float64(int64(v)) {
									params[key] = strconv.FormatInt(int64(v), 10)
								} else {
									params[key] = strconv.FormatFloat(v, 'f', -1, 64)
								}
							default:
								params[key] = fmt.Sprintf("%v", value)
							}
						}
					}
				}
			}
		}

		// 检查必需的签名参数
		timestamp, timestampExists := params["timestamp"]
		_, nonceExists := params["nonce"]
		sign, signExists := params["sign"]

		if !timestampExists || !nonceExists || !signExists {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "缺少必需的签名参数",
			})
			c.Abort()
			return
		}

		// 验证时间戳（5分钟内有效）
		timestampInt, err := strconv.ParseInt(timestamp, 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "时间戳格式错误",
			})
			c.Abort()
			return
		}

		currentTime := time.Now().Unix()
		if abs(currentTime-timestampInt) > 300 { // 5分钟
			c.JSON(http.StatusBadRequest, models.StandardResponse{
				Code: -1,
				Msg:  "请求已过期",
			})
			c.Abort()
			return
		}

		// 计算期望的签名
		expectedSign := calculateSign(params, appSecret)

		// 验证签名
		if sign != expectedSign {
			c.JSON(http.StatusUnauthorized, models.StandardResponse{
				Code: -1,
				Msg:  "签名验证失败",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// abs 计算绝对值
func abs(x int64) int64 {
	if x < 0 {
		return -x
	}
	return x
}
