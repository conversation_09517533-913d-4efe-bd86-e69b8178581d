package main

import (
	"fmt"
	"os"
	"strings"

	"iaa-gamelog/test/common"
)

// testLaunchConfig 测试游戏启动配置
func testLaunchConfig(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试游戏启动配置\n")

	testCases := []struct {
		name    string
		appName string
		version string
		expectSuccess bool
	}{
		{"正常获取配置", "test_game", "1.0.0", true},
		{"不同版本配置", "test_game", "2.0.0", true},
		{"不同游戏配置", "another_game", "1.0.0", true},
		{"缺少app_name参数", "", "1.0.0", false},
		{"缺少version参数", "test_game", "", false},
	}

	for _, tc := range testCases {
		fmt.Printf("  📋 %s\n", tc.name)

		url := fmt.Sprintf("%s/common/config/info?app_name=%s&version=%s",
			config.BaseURL, tc.appName, tc.version)

		resp, err := common.HTTPGet(config, url)
		if err != nil {
			if tc.expectSuccess {
				common.PrintTestResult(tc.name, false, err)
			} else {
				common.PrintTestResult(tc.name, true, nil)
			}
			continue
		}

		if tc.expectSuccess {
			if resp.Code == 0 {
				common.PrintTestResult(tc.name, true, nil)
				// 检查返回数据结构
				if data, ok := resp.Data.(map[string]interface{}); ok {
					fmt.Printf("     ✓ 配置包含: app_name=%v, version=%v\n", 
						data["app_name"], data["version"])
					if config, exists := data["config"]; exists {
						fmt.Printf("     ✓ 游戏配置: %v\n", config)
					}
				}
			} else {
				common.PrintTestResult(tc.name, false, fmt.Errorf("响应码: %d, 消息: %s", resp.Code, resp.Msg))
			}
		} else {
			if resp.Code != 0 {
				common.PrintTestResult(tc.name, true, nil)
			} else {
				common.PrintTestResult(tc.name, false, fmt.Errorf("应该返回错误但成功了"))
			}
		}
	}

	return nil
}

// testShareConfig 测试分享配置
func testShareConfig(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试分享配置\n")

	testCases := []struct {
		name    string
		appName string
		version string
	}{
		{"获取分享配置", "test_game", "1.0.0"},
		{"不同游戏分享配置", "social_game", "1.0.0"},
	}

	for _, tc := range testCases {
		fmt.Printf("  📋 %s\n", tc.name)

		url := fmt.Sprintf("%s/common/game/share_list?app_name=%s&version=%s",
			config.BaseURL, tc.appName, tc.version)

		resp, err := common.HTTPGet(config, url)
		if err != nil {
			common.PrintTestResult(tc.name, false, err)
			continue
		}

		if resp.Code == 0 {
			common.PrintTestResult(tc.name, true, nil)
			// 检查分享配置结构
			if data, ok := resp.Data.(map[string]interface{}); ok {
				if shares, exists := data["shares"]; exists {
					if shareList, ok := shares.([]interface{}); ok {
						fmt.Printf("     ✓ 分享配置数量: %d\n", len(shareList))
						for i, share := range shareList {
							if shareItem, ok := share.(map[string]interface{}); ok {
								fmt.Printf("     ✓ 分享项 %d: %s\n", i+1, shareItem["title"])
							}
						}
					}
				}
			}
		} else {
			common.PrintTestResult(tc.name, false, fmt.Errorf("响应码: %d, 消息: %s", resp.Code, resp.Msg))
		}
	}

	return nil
}

// testAdsConfig 测试广告配置
func testAdsConfig(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试广告配置\n")

	testCases := []struct {
		name    string
		appName string
		version string
	}{
		{"获取广告配置", "test_game", "1.0.0"},
		{"广告游戏配置", "ad_game", "1.0.0"},
	}

	for _, tc := range testCases {
		fmt.Printf("  📋 %s\n", tc.name)

		url := fmt.Sprintf("%s/common/game/ads?app_name=%s&version=%s",
			config.BaseURL, tc.appName, tc.version)

		resp, err := common.HTTPGet(config, url)
		if err != nil {
			common.PrintTestResult(tc.name, false, err)
			continue
		}

		if resp.Code == 0 {
			common.PrintTestResult(tc.name, true, nil)
			// 检查广告配置结构
			if data, ok := resp.Data.(map[string]interface{}); ok {
				if ads, exists := data["ads"]; exists {
					if adsConfig, ok := ads.(map[string]interface{}); ok {
						fmt.Printf("     ✓ 广告类型配置:\n")
						for adType, adConfig := range adsConfig {
							fmt.Printf("       - %s: %v\n", adType, adConfig)
						}
					}
				}
			}
		} else {
			common.PrintTestResult(tc.name, false, fmt.Errorf("响应码: %d, 消息: %s", resp.Code, resp.Msg))
		}
	}

	return nil
}

func main() {
	config := common.DefaultTestConfig()

	// 检查命令行参数
	if len(os.Args) > 1 {
		if os.Args[1] == "--quiet" {
			config.Verbose = false
		}
	}

	common.PrintTestHeader("BaseNet API - 游戏配置模块测试")
	fmt.Printf("服务器地址: %s\n", config.BaseURL)

	// 检查服务器状态
	if err := common.WaitForServer(config, 3); err != nil {
		fmt.Printf("❌ 服务器连接失败: %v\n", err)
		fmt.Println("请确保服务器已启动: ./gamelog.exe")
		os.Exit(1)
	}

	// 执行测试
	var hasError bool

	if err := testLaunchConfig(config); err != nil {
		hasError = true
	}

	if err := testShareConfig(config); err != nil {
		hasError = true
	}

	if err := testAdsConfig(config); err != nil {
		hasError = true
	}

	// 输出测试总结
	fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")
	if hasError {
		fmt.Println("❌ 游戏配置模块测试完成，存在失败项")
		os.Exit(1)
	} else {
		fmt.Println("✅ 游戏配置模块测试全部通过")
	}
}
