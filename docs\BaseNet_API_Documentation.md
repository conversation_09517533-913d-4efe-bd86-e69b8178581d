# BaseNet API 接口文档

## 概述

BaseNet.js 是一个游戏客户端网络请求库，包含了游戏运行所需的各种API接口。本文档详细描述了每个API的请求参数、返回参数和请求方式。

## 基础配置

### 服务器域名配置
```javascript
BaseUrl.ServerDomain = ""; // 主服务器域名，如: https://game.zuiqiangyingyu.net
BaseUrl.ServerSourceAPI = ""; // API服务器域名，如: https://api.wanjiazhuhua.com
BaseUrl.GetIpApi = "https://ifconfig.co"; // IP获取API
BaseUrl.ServerDomain_Open = ""; // 开放API域名，如: https://open-api.zuiqiangyingyu.net
```

### 请求签名机制
所有需要签名的请求都会添加以下参数：
- `t`: 客户端时间戳（秒）
- `nonce`: 随机字符串
- `sign`: MD5签名，计算方式为 MD5(参数按字母排序拼接 + AppSecret)

## API 接口列表

### 1. 游戏配置相关

#### 1.1 BMS_LAUNCH_CONFIG - 获取游戏启动配置
- **路径**: `/common/config/info`
- **请求方式**: GET
- **请求参数**:
  ```json
  {
    "app_name": "string", // 应用名称，如: "syfxmrtl_tt"
    "version": "string"   // 版本号，如: "2.1.9"
  }
  ```
- **返回参数**:
  ```json
  {
    "code": 0,
    "msg": "success",
    "data": {
      // 游戏配置数据
    }
  }
  ```

#### 1.2 BMS_SHARE_CONFIG - 获取分享配置
- **路径**: `/common/game/share_list`
- **请求方式**: GET
- **请求参数**:
  ```json
  {
    "app_name": "string",
    "version": "string"
  }
  ```

#### 1.3 BMS_TOFU_CONFIG - 获取广告配置
- **路径**: `/common/game/ads`
- **请求方式**: GET
- **请求参数**:
  ```json
  {
    "app_name": "string",
    "version": "string"
  }
  ```

### 2. 用户登录相关

#### 2.1 BMS_SIGN_IN_WX - 微信登录
- **路径**: `/common/session/sign_in`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "code": "string",     // 微信登录code
    "app_name": "string"  // 应用名称
  }
  ```
- **返回参数**:
  ```json
  {
    "code": 0,
    "msg": "success",
    "data": {
      "openid": "string",      // 用户openid
      "session_key": "string"  // 会话密钥
    }
  }
  ```

#### 2.2 BMS_SIGN_IN_BD - 百度登录
- **路径**: `/common/baidu/sign_in`
- **请求方式**: POST

#### 2.3 BMS_SIGN_IN_QQ - QQ登录
- **路径**: `/common/qqminiapp/sign_in`
- **请求方式**: POST

### 3. 数据存储相关

#### 3.1 DATA_SAVE - 保存游戏数据
- **路径**: `/common/game-data/s-save`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "app_name": "string",
    "version": "string",
    "uuid": "string",        // 用户openid
    "d_key": "string",       // 数据键名
    "d_data": "string",      // 数据内容（JSON字符串）
    "timestamp": 1234567890, // 时间戳
    "nonce": "string",       // 随机字符串
    "sign": "string"         // MD5签名
  }
  ```

#### 3.2 DATA_GET - 获取游戏数据
- **路径**: `/common/game-data/get`
- **请求方式**: GET
- **请求参数**:
  ```json
  {
    "app_name": "string",
    "version": "string",
    "uuid": "string",
    "d_key": "string"
  }
  ```

#### 3.3 DATA_MULTIGET - 批量获取游戏数据
- **路径**: `/common/game-data/multi-get`
- **请求方式**: GET
- **请求参数**:
  ```json
  {
    "app_name": "string",
    "version": "string",
    "uuid": "string",
    "d_keys": "string"  // 多个键名，逗号分隔
  }
  ```

### 4. 统计上报相关

#### 4.1 BMS_LOGIN_LOG - 登录日志统计
- **路径**: `/statistics/login_log`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "app_name": "string",
    "channel": "string",     // 渠道名称
    "version": "string",
    "uuid": "string",
    "platform": "string",   // 平台类型
    "device_info": "object" // 设备信息
  }
  ```

#### 4.2 BMS_GAME - 游戏行为统计
- **路径**: `/statistics/game`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "app_name": "string",
    "channel": "string",
    "version": "string",
    "uuid": "string",
    "m_data": "string",  // 统计数据JSON字符串
    "b_t": 1234567890    // 服务器时间戳
  }
  ```

#### 4.3 BMS_AD_SHOW - 广告展示统计
- **路径**: `/statistics/ad/show`
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "app_name": "string",
    "version": "string",
    "uuid": "string",
    "ad_type": "string",    // 广告类型: banner/video/interstitial
    "ad_id": "string",      // 广告位ID
    "platform": "string",  // 平台类型
    "timestamp": 1234567890
  }
  ```

#### 4.4 BMS_AD_HIT - 广告点击统计
- **路径**: `/statistics/ad/hit`
- **请求方式**: POST
- **请求参数**: 同 BMS_AD_SHOW

#### 4.5 BMS_SHARE_SHOW - 分享展示统计
- **路径**: `/statistics/share/show`
- **请求方式**: POST

#### 4.6 BMS_HINT - 提示统计
- **路径**: `/statistics/hint`
- **请求方式**: POST

### 5. 工具类接口

#### 5.1 BMS_SERVER_TIME - 获取服务器时间
- **路径**: `/common/common/time`
- **请求方式**: GET
- **返回参数**:
  ```json
  {
    "code": 0,
    "msg": "success",
    "data": {
      "time": 1234567890  // 服务器时间戳
    }
  }
  ```

#### 5.2 BMS_IP_IS_ENABLE - IP检查
- **路径**: `/common/is/is`
- **请求方式**: GET

#### 5.3 LOGINCODE - 登录码验证
- **路径**: `/common/login-code/check`
- **请求方式**: GET
- **请求参数**:
  ```json
  {
    "app_name": "string",
    "version": "string",
    "code": "string",      // 登录码
    "ca_code": "string"    // 可选，CA码
  }
  ```

### 6. 微信相关接口

#### 6.1 DECODE_DATA - 解密微信数据
- **路径**: `/common/wechat/decode_data`
- **请求方式**: POST

#### 6.2 BMS_CARD_SHARE - 分享卡片
- **路径**: `/common/share/hit`
- **请求方式**: POST

#### 6.3 BMS_CARD_SHARE_INFO - 分享信息
- **路径**: `/common/share/info`
- **请求方式**: GET

### 7. 头条相关接口

#### 7.1 ANTIDIRT - 内容审核
- **路径**: `/common/toutiao/antidirt`
- **请求方式**: POST

#### 7.2 GIFT_RECEIVE_REWARD - 礼品奖励
- **路径**: `/common/toutiao/gift-receive-reward`
- **请求方式**: POST

#### 7.3 GET_UNCONSUMED_CIRCLE_GIFTS - 获取未消费圈子礼品
- **路径**: `/common/toutiao/get-unconsumed-circle-gifts`
- **请求方式**: GET

#### 7.4 CONSUME_CIRCLE_GIFT - 消费圈子礼品
- **路径**: `/common/toutiao/consume-circle-gift`
- **请求方式**: POST

### 8. 其他接口

#### 8.1 GETADVINFO - 获取广告信息
- **路径**: `/api/sdk/yw/adv/getAdvInfo`
- **请求方式**: GET

#### 8.2 MATERIALSS - 素材接口
- **路径**: `/common/ads/material-ss`
- **请求方式**: GET

#### 8.3 UUID_IN_WHITE_LIST - UUID白名单检查
- **路径**: `/check/Uuidwhitelist/uuidInWhitelist`
- **请求方式**: GET

## 通用响应格式

所有API接口都遵循统一的响应格式：

```json
{
  "code": 0,           // 状态码，0表示成功
  "msg": "success",    // 状态消息
  "data": {}          // 响应数据
}
```

### 错误码说明
- `0`: 成功
- `-1`: 网络超时或连接错误
- `其他`: 业务错误码

## 请求配置

### 超时设置
- GET请求超时: 5秒
- POST请求超时: 7秒

### 请求头设置
- GET请求: `Content-Type: application/json`
- POST请求: `Content-Type: application/x-www-form-urlencoded`
- 原生应用: 额外添加 `Accept-Encoding: gzip,deflate`

## 签名算法示例

```javascript
function getSign(params, secret) {
    params.timestamp = Math.round(Date.now() / 1000);
    params.nonce = md5((params.timestamp + Math.random() * 1000).toString());
    
    // 参数按字母排序
    const sortedParams = {};
    Object.keys(params).sort().forEach(key => {
        sortedParams[key] = params[key];
    });
    
    // 拼接参数
    const paramStr = Object.keys(sortedParams)
        .map(key => `${key}=${sortedParams[key]}`)
        .join('&');
    
    // 计算签名
    params.sign = md5(paramStr + secret);
    
    return params;
}
```

## 平台配置

不同平台使用不同的应用配置，详见 SdkConfig.js 中的 BMSInfoList 配置。

主要平台包括：
- 微信小游戏 (WECHAT_GAME)
- 字节跳动 (BYTE_DANCE)
- QQ小游戏 (QQ)
- 百度小游戏 (BAIDU)
- 快手小游戏 (KWAI_MICRO)
- 各种Android渠道

每个平台都有对应的：
- APP_NAME: 应用名称
- VERSION: 版本号
- CHANNEL_NAME: 渠道名称
