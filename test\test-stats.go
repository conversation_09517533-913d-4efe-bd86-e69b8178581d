package main

import (
	"fmt"
	"os"
	"strings"
	"time"

	"iaa-gamelog/test/common"
)

// LoginLogRequest 登录日志请求
type LoginLogRequest struct {
	AppName    string      `json:"app_name"`
	Channel    string      `json:"channel"`
	Version    string      `json:"version"`
	UUID       string      `json:"uuid"`
	Platform   string      `json:"platform"`
	DeviceInfo interface{} `json:"device_info"`
}

// GameStatRequest 游戏行为统计请求
type GameStatRequest struct {
	AppName string `json:"app_name"`
	Channel string `json:"channel"`
	Version string `json:"version"`
	UUID    string `json:"uuid"`
	MData   string `json:"m_data"`
	BT      int64  `json:"b_t"`
}

// AdStatRequest 广告统计请求
type AdStatRequest struct {
	AppName   string `json:"app_name"`
	Version   string `json:"version"`
	UUID      string `json:"uuid"`
	AdType    string `json:"ad_type"`
	AdID      string `json:"ad_id"`
	Platform  string `json:"platform"`
	Timestamp int64  `json:"timestamp"`
}

// testLoginLog 测试登录日志统计
func testLoginLog(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试登录日志统计\n")

	testCases := []struct {
		name       string
		appName    string
		channel    string
		uuid       string
		platform   string
		deviceInfo map[string]interface{}
	}{
		{
			name:     "微信小程序登录",
			appName:  "test_game",
			channel:  "wechat",
			uuid:     "wx_user_001",
			platform: "wechat",
			deviceInfo: map[string]interface{}{
				"model":   "iPhone 13",
				"system":  "iOS 15.0",
				"network": "wifi",
				"brand":   "Apple",
			},
		},
		{
			name:     "百度小程序登录",
			appName:  "test_game",
			channel:  "baidu",
			uuid:     "bd_user_002",
			platform: "baidu",
			deviceInfo: map[string]interface{}{
				"model":   "Xiaomi 12",
				"system":  "Android 12",
				"network": "4g",
				"brand":   "Xiaomi",
			},
		},
	}

	for _, tc := range testCases {
		fmt.Printf("  📊 %s\n", tc.name)

		loginLogReq := LoginLogRequest{
			AppName:    tc.appName,
			Channel:    tc.channel,
			Version:    "1.0.0",
			UUID:       tc.uuid,
			Platform:   tc.platform,
			DeviceInfo: tc.deviceInfo,
		}

		url := fmt.Sprintf("%s/statistics/login_log", config.BaseURL)
		resp, err := common.HTTPPost(config, url, loginLogReq)
		if err != nil {
			common.PrintTestResult(tc.name, false, err)
			continue
		}

		if resp.Code == 0 {
			common.PrintTestResult(tc.name, true, nil)
			fmt.Printf("     ✓ 用户: %s, 平台: %s\n", tc.uuid, tc.platform)
		} else {
			common.PrintTestResult(tc.name, false, fmt.Errorf("响应码: %d, 消息: %s", resp.Code, resp.Msg))
		}
	}

	return nil
}

// testGameStats 测试游戏行为统计
func testGameStats(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试游戏行为统计\n")

	testCases := []struct {
		name    string
		appName string
		uuid    string
		mData   string
		bt      int64
	}{
		{
			name:    "关卡完成统计",
			appName: "test_game",
			uuid:    "player_001",
			mData:   `{"action":"level_complete","level":5,"score":1200,"time":180}`,
			bt:      time.Now().Unix(),
		},
		{
			name:    "道具使用统计",
			appName: "test_game",
			uuid:    "player_002",
			mData:   `{"action":"item_use","item":"health_potion","count":1}`,
			bt:      time.Now().Unix(),
		},
		{
			name:    "购买行为统计",
			appName: "test_game",
			uuid:    "player_003",
			mData:   `{"action":"purchase","item":"premium_pack","price":9.99,"currency":"USD"}`,
			bt:      time.Now().Unix(),
		},
	}

	for _, tc := range testCases {
		fmt.Printf("  🎮 %s\n", tc.name)

		gameStatReq := GameStatRequest{
			AppName: tc.appName,
			Channel: "default",
			Version: "1.0.0",
			UUID:    tc.uuid,
			MData:   tc.mData,
			BT:      tc.bt,
		}

		url := fmt.Sprintf("%s/statistics/game", config.BaseURL)
		resp, err := common.HTTPPost(config, url, gameStatReq)
		if err != nil {
			common.PrintTestResult(tc.name, false, err)
			continue
		}

		if resp.Code == 0 {
			common.PrintTestResult(tc.name, true, nil)
			fmt.Printf("     ✓ 用户: %s, 行为数据: %s\n", tc.uuid, tc.mData)
		} else {
			common.PrintTestResult(tc.name, false, fmt.Errorf("响应码: %d, 消息: %s", resp.Code, resp.Msg))
		}
	}

	return nil
}

// testAdStats 测试广告统计
func testAdStats(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试广告统计\n")

	// 测试广告展示统计
	fmt.Printf("  📺 广告展示统计\n")
	adShowReq := AdStatRequest{
		AppName:   "test_game",
		Version:   "1.0.0",
		UUID:      "player_001",
		AdType:    "banner",
		AdID:      "banner_ad_001",
		Platform:  "wechat",
		Timestamp: time.Now().Unix(),
	}

	url := fmt.Sprintf("%s/statistics/ad/show", config.BaseURL)
	resp, err := common.HTTPPost(config, url, adShowReq)
	if err != nil {
		common.PrintTestResult("广告展示统计", false, err)
	} else if resp.Code == 0 {
		common.PrintTestResult("广告展示统计", true, nil)
		fmt.Printf("     ✓ 广告类型: %s, 广告ID: %s\n", adShowReq.AdType, adShowReq.AdID)
	} else {
		common.PrintTestResult("广告展示统计", false, fmt.Errorf("响应码: %d, 消息: %s", resp.Code, resp.Msg))
	}

	// 测试广告点击统计
	fmt.Printf("  👆 广告点击统计\n")
	adHitReq := AdStatRequest{
		AppName:   "test_game",
		Version:   "1.0.0",
		UUID:      "player_001",
		AdType:    "video",
		AdID:      "video_ad_002",
		Platform:  "wechat",
		Timestamp: time.Now().Unix(),
	}

	url = fmt.Sprintf("%s/statistics/ad/hit", config.BaseURL)
	resp, err = common.HTTPPost(config, url, adHitReq)
	if err != nil {
		common.PrintTestResult("广告点击统计", false, err)
	} else if resp.Code == 0 {
		common.PrintTestResult("广告点击统计", true, nil)
		fmt.Printf("     ✓ 广告类型: %s, 广告ID: %s\n", adHitReq.AdType, adHitReq.AdID)
	} else {
		common.PrintTestResult("广告点击统计", false, fmt.Errorf("响应码: %d, 消息: %s", resp.Code, resp.Msg))
	}

	return nil
}

func main() {
	config := common.DefaultTestConfig()

	// 检查命令行参数
	if len(os.Args) > 1 {
		if os.Args[1] == "--quiet" {
			config.Verbose = false
		}
	}

	common.PrintTestHeader("BaseNet API - 统计上报模块测试")
	fmt.Printf("服务器地址: %s\n", config.BaseURL)

	// 检查服务器状态
	if err := common.WaitForServer(config, 3); err != nil {
		fmt.Printf("❌ 服务器连接失败: %v\n", err)
		fmt.Println("请确保服务器已启动: ./gamelog.exe")
		os.Exit(1)
	}

	// 执行测试
	var hasError bool

	if err := testLoginLog(config); err != nil {
		hasError = true
	}

	if err := testGameStats(config); err != nil {
		hasError = true
	}

	if err := testAdStats(config); err != nil {
		hasError = true
	}

	// 输出测试总结
	fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")
	if hasError {
		fmt.Println("❌ 统计上报模块测试完成，存在失败项")
		os.Exit(1)
	} else {
		fmt.Println("✅ 统计上报模块测试全部通过")
	}
}
