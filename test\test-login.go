package main

import (
	"fmt"
	"os"
	"strings"

	"iaa-gamelog/test/common"
)

// LoginRequest 登录请求
type LoginRequest struct {
	Code    string `json:"code"`
	AppName string `json:"app_name"`
}

// testWechatLogin 测试微信登录
func testWechatLogin(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试微信登录\n")

	testCases := []struct {
		name          string
		code          string
		appName       string
		expectSuccess bool
	}{
		{"正常微信登录", "wx_test_code_123", "test_game", true},
		{"长微信登录码", "wx_very_long_authorization_code_456789", "test_game", true},
		{"缺少code参数", "", "test_game", false},
		{"缺少app_name参数", "wx_test_code", "", false},
	}

	for _, tc := range testCases {
		fmt.Printf("  📱 %s\n", tc.name)

		loginReq := LoginRequest{
			Code:    tc.code,
			AppName: tc.appName,
		}

		url := fmt.Sprintf("%s/common/session/sign_in", config.BaseURL)
		resp, err := common.HTTPPost(config, url, loginReq)
		if err != nil {
			if tc.expectSuccess {
				common.PrintTestResult(tc.name, false, err)
			} else {
				common.PrintTestResult(tc.name, true, nil)
			}
			continue
		}

		if tc.expectSuccess {
			if resp.Code == 0 {
				common.PrintTestResult(tc.name, true, nil)
				// 检查返回的登录数据
				if data, ok := resp.Data.(map[string]interface{}); ok {
					fmt.Printf("     ✓ openid: %v\n", data["openid"])
					fmt.Printf("     ✓ session_key: %v\n", data["session_key"])
					if unionid, exists := data["unionid"]; exists {
						fmt.Printf("     ✓ unionid: %v\n", unionid)
					}
				}
			} else {
				common.PrintTestResult(tc.name, false, fmt.Errorf("响应码: %d, 消息: %s", resp.Code, resp.Msg))
			}
		} else {
			if resp.Code != 0 {
				common.PrintTestResult(tc.name, true, nil)
			} else {
				common.PrintTestResult(tc.name, false, fmt.Errorf("应该返回错误但成功了"))
			}
		}
	}

	return nil
}

// testBaiduLogin 测试百度登录
func testBaiduLogin(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试百度登录\n")

	testCases := []struct {
		name    string
		code    string
		appName string
	}{
		{"正常百度登录", "bd_test_code_123", "test_game"},
		{"百度小程序登录", "bd_miniapp_code_456", "baidu_game"},
	}

	for _, tc := range testCases {
		fmt.Printf("  🔵 %s\n", tc.name)

		loginReq := LoginRequest{
			Code:    tc.code,
			AppName: tc.appName,
		}

		url := fmt.Sprintf("%s/common/baidu/sign_in", config.BaseURL)
		resp, err := common.HTTPPost(config, url, loginReq)
		if err != nil {
			common.PrintTestResult(tc.name, false, err)
			continue
		}

		if resp.Code == 0 {
			common.PrintTestResult(tc.name, true, nil)
			// 检查返回的登录数据
			if data, ok := resp.Data.(map[string]interface{}); ok {
				fmt.Printf("     ✓ openid: %v\n", data["openid"])
				fmt.Printf("     ✓ session_key: %v\n", data["session_key"])
			}
		} else {
			common.PrintTestResult(tc.name, false, fmt.Errorf("响应码: %d, 消息: %s", resp.Code, resp.Msg))
		}
	}

	return nil
}

// testQQLogin 测试QQ登录
func testQQLogin(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试QQ登录\n")

	testCases := []struct {
		name    string
		code    string
		appName string
	}{
		{"正常QQ登录", "qq_test_code_123", "test_game"},
		{"QQ小程序登录", "qq_miniapp_code_456", "qq_game"},
	}

	for _, tc := range testCases {
		fmt.Printf("  🐧 %s\n", tc.name)

		loginReq := LoginRequest{
			Code:    tc.code,
			AppName: tc.appName,
		}

		url := fmt.Sprintf("%s/common/qqminiapp/sign_in", config.BaseURL)
		resp, err := common.HTTPPost(config, url, loginReq)
		if err != nil {
			common.PrintTestResult(tc.name, false, err)
			continue
		}

		if resp.Code == 0 {
			common.PrintTestResult(tc.name, true, nil)
			// 检查返回的登录数据
			if data, ok := resp.Data.(map[string]interface{}); ok {
				fmt.Printf("     ✓ openid: %v\n", data["openid"])
				fmt.Printf("     ✓ session_key: %v\n", data["session_key"])
			}
		} else {
			common.PrintTestResult(tc.name, false, fmt.Errorf("响应码: %d, 消息: %s", resp.Code, resp.Msg))
		}
	}

	return nil
}

func main() {
	config := common.DefaultTestConfig()

	// 检查命令行参数
	if len(os.Args) > 1 {
		if os.Args[1] == "--quiet" {
			config.Verbose = false
		}
	}

	common.PrintTestHeader("BaseNet API - 用户登录模块测试")
	fmt.Printf("服务器地址: %s\n", config.BaseURL)

	// 检查服务器状态
	if err := common.WaitForServer(config, 3); err != nil {
		fmt.Printf("❌ 服务器连接失败: %v\n", err)
		fmt.Println("请确保服务器已启动: ./gamelog.exe")
		os.Exit(1)
	}

	// 执行测试
	var hasError bool

	if err := testWechatLogin(config); err != nil {
		hasError = true
	}

	if err := testBaiduLogin(config); err != nil {
		hasError = true
	}

	if err := testQQLogin(config); err != nil {
		hasError = true
	}

	// 输出测试总结
	fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")
	if hasError {
		fmt.Println("❌ 用户登录模块测试完成，存在失败项")
		os.Exit(1)
	} else {
		fmt.Println("✅ 用户登录模块测试全部通过")
	}
}
