{"code": 0, "msg": "成功", "data": {"GM": 1, "管理员开关": "  1：开;  0:关", "isLog": 1, "log开关": "  1：开;  0:关", "isTaLog": 0, "isLive": 0, "无广GM配置": "1开,0关", "rateUs": 2, "好评开启关卡": "关卡id", "wxUpdateType": 0, "微信是否强制更新": "  1：是;  0:否", "initialStamina": 30, "初始体力": " 体力值", "isEnterBackpack": 0, "新用户初始显示配置": "1进入背包,0进入主界面", "m20guideToggle": [0, 0], "新手指引开关": " 功能开关; 是否显示可跳过引导", "staminaCd": 300, "补充体力时间间隔秒": "值", "fightStamina": 5, "普通关卡所需体力值": "值", "adBuyStamina": [1, 20, 10], "广告购买体力值": "花费值,获得体力值,每日购买上限", "diamondBuyStamina": [150, 30, 5], "钻石购买体力值": "花费值,获得体力值,每日购买上限", "lvSweep": [3, 10, 3], "扫荡配置": "体力扫荡次数,广告扫荡次数,看广告增加几次体力扫荡次数", "activity": 20, "挑战次数": "值", "activityWhiteList": 99, "挑战次数白名单": "值", "barrelHp": [0.5, 2], "炸药桶血量随机区间配置": "配表的血量*随机的值,0.1步长", "barrelMoveWeight": 0.3, "炸药桶爆炸触发移动概率": "值", "rocklHp": [0.5, 2], "挖矿龙石块血量随机区间配置": "配表的血量*随机的值,0.1步长", "goldMineBox": [[140, 40020, 70], [240, 40021, 80], [400, 40022, 20], [800, 40023, 5]], "挖矿龙金矿宝箱": "金矿值,poollistid,权重", "treasureRewardWeight": [[17, 30]], "挖矿龙最后宝箱奖励权重": "货币id,增加权重", "drawBox": [[50, 60000, 70], [100, 60001, 80], [180, 60002, 20], [300, 60003, 5]], "画线宝箱": "金币值,poollistid,权重", "deadAnim": 0, "怪物死亡动画微信备案": "1开0关", "shopAdGift": [[2, 3, 4], [1000, 5, 5]], "商店免广告礼包配置": "数量和权重", "shopConfig": [[11, 10], [2, 10], [9, 10], [25, 5]], "商店商品配置": "货币类型,购买次数", "eliteLv": 1, "精英关卡开关配置": "1开,0关", "subscriptionLv": 3, "订阅弹出关卡无论输赢回到主界面拉起订阅": "关卡id", "minigameSwitch": 0, "买量副玩法加白开关": "1开0关", "shareCfg": {"1001": {"id": 2682, "num": 0}, "1002": {"id": 2683, "num": 1}, "1003": {"id": 2684, "num": 1}, "1004": {"id": 2684, "num": 1}}, "分享广告场景": "分享id,分享次数", "dailySignShareCfg": {"2": {"id": 2694, "num": 0}, "4": {"id": 2694, "num": 0}, "6": {"id": 2694, "num": 0}}, "签到分享场景": "天数,分享id", "dragonBoxFree": [[1, 1, 3, 3], [2, 1, 3, 0]], "普通关卡龙宝箱次数配置": "蓝宝箱,免费刷新次数,广告刷新次数,全都要次数,红宝箱,免费刷新次数,广告刷新次数,全都要次数", "dragonRefreshSwitch": 1, "龙玩法buff权重": "紫色金色词条", "dragonRefreshBuff": [60, 60], "diffSelect": 1, "龙难度选择开关": "  1：开;  0:关", "diffSelectCfg": [[1.5, 1, 6], [2, 1.1, 8]], "龙难度选择配置": " 困难血量移速,地狱血量移速,第几章解锁难度", "dragonBossDiff": [[3025, 1, 0.35, 1, 1], [3025, 1, 0, 0.35, 2], [3025, 2, 0.5, 1, 1], [3025, 2, 0.25, 0.5, 2], [3025, 2, 0, 0.25, 3]], "dragonRevive": 3, "龙普通关卡复活次数": "值", "dragonAdRepelTime": 6, "主线关卡广告复活龙击退时间": "值", "dragonCrzay": [[0, 5, 0.1, -1], [5, 10, 0.12, -1], [10, 15, 0.14, -1], [15, 20, 0.16, -1], [20, 25, 0.18, -1], [25, 30, 0.2, -1], [30, 35, 0.22, -1], [35, 40, 0.24, -1], [40, 45, 0.26, -1], [45, 50, 0.28, -1], [50, 55, 0.3, -1], [55, 60, 0.32, -1], [60, 65, 0.34, -1], [65, 70, 0.4, -1], [70, 100, 0.45, -1], [100, 999, 0.65, -1], [0, 5, 0.32, 60006], [5, 10, 0.34, 60006], [10, 15, 0.36, 60006], [15, 20, 0.38, 60006], [20, 25, 0.4, 60006], [25, 30, 0.42, 60006], [30, 35, 0.44, 60006], [35, 40, 0.46, 60006], [40, 45, 0.48, 60006], [45, 50, 0.5, 60006], [50, 55, 0.52, 60006], [55, 60, 0.54, 60006], [60, 65, 0.56, 60006], [65, 70, 0.58, 60006], [70, 100, 0.6, 60006], [100, 999, 0.75, 60006], [0, 5, 0.32, 60008], [5, 10, 0.34, 60008], [10, 15, 0.36, 60008], [15, 20, 0.38, 60008], [20, 25, 0.4, 60008], [25, 30, 0.42, 60008], [30, 35, 0.44, 60008], [35, 40, 0.46, 60008], [40, 45, 0.48, 60008], [45, 50, 0.5, 60008], [50, 55, 0.52, 60008], [55, 60, 0.54, 60008], [60, 65, 0.56, 60008], [65, 70, 0.58, 60008], [70, 100, 0.6, 60008], [100, 999, 0.75, 60008], [0, 5, 0.32, 60009], [5, 10, 0.34, 60009], [10, 15, 0.36, 60009], [15, 20, 0.38, 60009], [20, 25, 0.4, 60009], [25, 30, 0.42, 60009], [30, 35, 0.44, 60009], [35, 40, 0.46, 60009], [40, 45, 0.48, 60009], [45, 50, 0.5, 60009], [50, 55, 0.52, 60009], [55, 60, 0.54, 60009], [60, 65, 0.56, 60009], [65, 70, 0.58, 60009], [70, 100, 0.6, 60009], [100, 999, 0.75, 60009], [0, 5, 0.4, 60048], [5, 10, 0.43, 60048], [10, 15, 0.46, 60048], [15, 20, 0.49, 60048], [20, 25, 0.52, 60048], [25, 30, 0.55, 60048], [30, 35, 0.58, 60048], [35, 40, 0.61, 60048], [40, 45, 0.64, 60048], [45, 50, 0.67, 60048], [50, 55, 0.7, 60048], [55, 60, 0.73, 60048], [60, 65, 0.76, 60048], [65, 70, 0.79, 60048], [70, 150, 0.82, 60048], [150, 999, 0.85, 60048]], "龙玩法狂暴配置": "身体区间,狂飙概率,关卡id默认-1", "dragonDiff": [[60000, 60, 70, 0.2], [60001, 30, 50, 0.25], [60001, 50, 70, 0.55], [60002, 30, 45, 0.3], [60002, 45, 70, 0.6], [60003, 30, 40, 0.35], [60003, 40, 70, 0.65], [60004, 30, 40, 1], [60004, 40, 70, 1.7], [60007, 30, 55, 1.4], [60007, 55, 98, 2.2], [60005, 30, 50, 1.3], [60005, 50, 98, 2.6], [60006, 20, 45, 2.5], [60006, 45, 98, 4], [60008, 20, 40, 1.2], [60008, 40, 98, 2.4], [60009, 20, 40, 1], [60009, 40, 98, 2.2], [60010, 30, 40, 0.1], [60010, 40, 140, 0.1], [60011, 30, 40, 0.3], [60011, 40, 95, 0.5], [60013, 30, 40, 0.6], [60013, 40, 95, 1.1], [60012, 30, 40, 0.15], [60012, 40, 95, 0.3], [60014, 30, 40, 0.15], [60014, 40, 95, 0.45], [60015, 30, 40, 0.5], [60015, 40, 95, 1.2], [60016, 30, 40, 0.8], [60016, 40, 95, 1.4], [60017, 30, 40, 0.6], [60017, 40, 95, 1], [60018, 30, 40, 0.55], [60018, 40, 95, 0.8], [60019, 30, 40, 0.7], [60019, 40, 95, 0.9], [60020, 30, 40, 1.3], [60020, 40, 95, 1.35], [60021, 30, 40, 1.3], [60021, 40, 95, 1.4], [60022, 30, 40, 1.3], [60022, 40, 95, 1.4], [60023, 30, 40, 1.3], [60023, 40, 95, 1.4], [60026, 10, 20, 0.4], [60026, 30, 40, 1.2], [60026, 40, 199, 1.8], [60027, 1, 40, 0.4], [60027, 40, 199, 0.7], [60030, 15, 40, 0.05], [60030, 75, 150, 0.1], [60031, 15, 40, 0.5], [60031, 75, 150, 0.7], [60033, 25, 75, 0.7], [60033, 75, 180, 1.5], [60034, 25, 75, 0.85], [60034, 75, 180, 1.4], [60035, 25, 75, 0.85], [60035, 75, 180, 1.4], [60036, 25, 75, 0.65], [60036, 75, 180, 1.35], [60037, 25, 75, 0.65], [60037, 75, 180, 1.35], [60038, 25, 75, 0.65], [60038, 75, 180, 1.35], [60039, 25, 75, 0.65], [60039, 75, 180, 1.35], [60041, 25, 75, 0.64], [60041, 75, 180, 1.34], [60047, 25, 75, 1], [60047, 75, 180, 2], [60051, 25, 75, 0.6], [60051, 75, 180, 1.4], [60052, 15, 40, 0.15], [60052, 40, 199, 0.3], [60053, 15, 40, 0.2], [60053, 40, 199, 0.45], [60024, 30, 40, 1.6], [60024, 40, 495, 2.3], [60032, 25, 75, 0.8], [60032, 75, 180, 1.5], [60032, 180, 200, 2], [60044, 20, 90, 0.75], [60044, 91, 180, 1.7], [60045, 20, 90, 0.75], [60045, 91, 180, 2], [60046, 60, 90, 0.4], [60046, 91, 180, 1], [60049, 100, 200, 0.5], [60049, 200, 270, 1], [60025, 50, 250, 0.2], [60025, 250, 300, 0.6], [60028, 20, 80, 1], [60028, 80, 300, 2.5], [60029, 50, 150, 0.2], [60029, 150, 300, 1.5], [60040, 50, 250, 0.2], [60040, 250, 300, 0.5], [60050, 50, 200, 0.4], [60050, 200, 280, 1], [60042, 15, 86, 0.2], [60042, 88, 200, 0.4]], "龙玩法血量配置": "关卡id,身体区间,血量加成", "dragonLvHardDiff": [[2, 1, 1, 1001], [2, 1, 1, 2001]], "关卡id龙血量配置": "生命,攻击,移速,关卡id", "whiteList": [1, 1], "白名单配置": "开关和显示联系我们", "dailysign": 3, "签到配置": "第几关后开启", "task": 999999, "任务配置": "第几关后开启", "actCoinSweep": 1, "金币挑战扫荡配置": "1开,0关", "speedUnlock": 2, "关卡加速配置": "2,所有速度免费开启,1本关有效,隐藏4倍数; 0永久解锁,显示4倍数", "gameSpeed": 1.5, "游戏默认速度配置": "值", "equip3SelectShow": [0, 1, 4, 6, 6], "装备升级开关": "1装备升级显示且需要升级后才解锁3选1逻辑,0关闭", "equip3selectAd": -1, "进阶3选1是否要广告": "1需要,0不需要", "miniGameLv": [], "副玩法隐藏关卡": "关卡id", "miniGameAdUnlock": 0, "副玩法广告解锁次数增量": "值", "redeemCode": [{"name": "normal", "code": "8z888", "reward": [[17, 1]], "type": 1, "startTime": 1723564800, "endTime": 1787299371}, {"name": "normal", "code": "wqsp", "reward": [[1009, 3000], [1010, 3000], [1011, 3000], [1012, 3000], [1013, 3000], [1014, 3000], [1015, 3000], [1016, 3000], [1017, 3000], [1018, 3000], [1019, 3000]], "type": 1, "startTime": 1723564800, "endTime": 1787299371}, {"name": "normal", "code": "wqsp2", "reward": [[1019, 5000]], "type": 1, "startTime": 1723564800, "endTime": 1787299371}, {"name": "normal", "code": "testReward", "reward": [[2, 10]], "type": 1, "openid": ["LJMbr06m", "sNreX_4X"], "startTime": 1723564800, "endTime": 1787299371}, {"name": "normal", "code": "testReward2", "reward": [[2, 10]], "type": 1, "openid": ["pI9w3a7w"], "startTime": 1723564800, "endTime": 1787299371}, {"name": "daily", "code": "test123", "reward": [[2, 1000, 150], [2, 2000, 100], [1, 10, 150], [1, 15, 120], [11, 100, 100], [11, 150, 90], [11, 200, 80], [1016, 2, 50], [1012, 5, 50], [1015, 5, 50], [1009, 10, 50], [1013, 10, 50], [40000, 2, 10], [40001, 2, 10], [40002, 2, 10], [53, 1, 20], [53, 3, 10], [53, 5, 5], [8060, 10, 80], [8060, 30, 10], [8061, 10, 70], [8061, 20, 10], [8081, 2, 30], [8082, 1, 5], [8083, 1, 3], [8084, 1, 2], [8031, 30, 60], [8031, 50, 30], [8033, 15, 10], [8033, 30, 10], [17, 1, 10], [17, 2, 3], [17, 3, 2], [17, 5, 1]], "rewardCount": [[1, 10], [2, 10]], "type": 1, "startTime": 1723564800, "endTime": 1787299371}, {"name": "daily", "code": "CONTENT573393922", "reward": [[17, 1, 80], [17, 2, 20], [17, 3, 5], [17, 4, 1], [17, 5, 1], [17, 6, 0], [17, 7, 0], [17, 8, 0], [17, 10, 0]], "rewardCount": [[1, 10]], "type": 1, "startTime": 1723564800, "endTime": 1787299371}, {"name": "daily", "code": "CONTENT", "reward": [[17, 1, 80], [17, 2, 20], [17, 3, 5], [17, 4, 1], [17, 5, 1], [17, 6, 0], [17, 7, 0], [17, 8, 0], [17, 10, 0]], "rewardCount": [[1, 10]], "type": 1, "startTime": 1723564800, "endTime": 1787299371}, {"name": "act", "code": "fkxb336", "reward": [[1, 2], [1, 2], [1, 2]], "type": 2, "startTime": 1, "endTime": 1}, {"name": "blogger", "code": "fkxb123", "reward": [[1, 2], [1, 2], [1, 2]], "type": 1, "startTime": 1723564800, "endTime": 1725119999}]}}