package handlers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"iaa-gamelog/internal/models"
)

// GetLaunchConfig BMS_LAUNCH_CONFIG - 获取游戏启动配置
func GetLaunchConfig(c *gin.Context) {
	appName := c.Query("app_name")
	version := c.Query("version")

	if appName == "" || version == "" {
		c.J<PERSON>N(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "缺少必要参数",
		})
		return
	}

	// 模拟配置数据，实际应用中应该从数据库或配置文件读取
	configData := map[string]interface{}{
		"app_name":    appName,
		"version":     version,
		"server_time": time.Now().Unix(),
		"config": map[string]interface{}{
			"max_level":    100,
			"daily_reward": true,
			"maintenance":  false,
			"update_url":   "",
			"force_update": false,
		},
	}

	c.<PERSON>(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: configData,
	})
}

// GetShareConfig BMS_SHARE_CONFIG - 获取分享配置
func GetShareConfig(c *gin.Context) {
	appName := c.Query("app_name")
	version := c.Query("version")

	if appName == "" || version == "" {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "缺少必要参数",
		})
		return
	}

	// 模拟分享配置数据
	shareData := map[string]interface{}{
		"app_name": appName,
		"version":  version,
		"shares": []map[string]interface{}{
			{
				"id":          1,
				"title":       "分享游戏给好友",
				"description": "邀请好友一起玩游戏",
				"image_url":   "https://example.com/share1.jpg",
				"reward":      100,
			},
			{
				"id":          2,
				"title":       "分享到朋友圈",
				"description": "分享游戏到朋友圈",
				"image_url":   "https://example.com/share2.jpg",
				"reward":      200,
			},
		},
	}

	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: shareData,
	})
}

// GetAdsConfig BMS_TOFU_CONFIG - 获取广告配置
func GetAdsConfig(c *gin.Context) {
	appName := c.Query("app_name")
	version := c.Query("version")

	if appName == "" || version == "" {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "缺少必要参数",
		})
		return
	}

	// 模拟广告配置数据
	adsData := map[string]interface{}{
		"app_name": appName,
		"version":  version,
		"ads": map[string]interface{}{
			"banner": map[string]interface{}{
				"enabled":    true,
				"ad_unit_id": "banner_ad_unit_123",
				"refresh":    30,
			},
			"video": map[string]interface{}{
				"enabled":    true,
				"ad_unit_id": "video_ad_unit_456",
				"reward":     50,
			},
			"interstitial": map[string]interface{}{
				"enabled":    true,
				"ad_unit_id": "interstitial_ad_unit_789",
				"frequency":  3,
			},
		},
	}

	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: adsData,
	})
}
