package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"iaa-gamelog/internal/models"
)

// SignInWechat BMS_SIGN_IN_WX - 微信登录
func SignInWechat(c *gin.Context) {
	var req models.SignInRequest
	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "请求参数错误",
		})
		return
	}

	if req.Code == "" || req.AppName == "" {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "缺少必要参数",
		})
		return
	}

	// 模拟微信登录处理
	// 实际应用中应该调用微信API验证code
	loginData := map[string]interface{}{
		"openid":      "wx_" + req.Code + "_openid",
		"session_key": "session_key_" + req.Code,
		"unionid":     "union_" + req.Code,
	}

	c.<PERSON>(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: loginData,
	})
}

// SignInBaidu BMS_SIGN_IN_BD - 百度登录
func SignInBaidu(c *gin.Context) {
	var req models.SignInRequest
	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "请求参数错误",
		})
		return
	}

	if req.Code == "" || req.AppName == "" {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "缺少必要参数",
		})
		return
	}

	// 模拟百度登录处理
	loginData := map[string]interface{}{
		"openid":      "bd_" + req.Code + "_openid",
		"session_key": "session_key_" + req.Code,
	}

	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: loginData,
	})
}

// SignInQQ BMS_SIGN_IN_QQ - QQ登录
func SignInQQ(c *gin.Context) {
	var req models.SignInRequest
	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "请求参数错误",
		})
		return
	}

	if req.Code == "" || req.AppName == "" {
		c.JSON(http.StatusBadRequest, models.StandardResponse{
			Code: -1,
			Msg:  "缺少必要参数",
		})
		return
	}

	// 模拟QQ登录处理
	loginData := map[string]interface{}{
		"openid":      "qq_" + req.Code + "_openid",
		"session_key": "session_key_" + req.Code,
	}

	c.JSON(http.StatusOK, models.StandardResponse{
		Code: 0,
		Msg:  "success",
		Data: loginData,
	})
}
