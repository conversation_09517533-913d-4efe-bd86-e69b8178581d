package database

// MongoAdapter MongoDB适配器，实现DatabaseInterface接口
type MongoAdapter struct {
	mongoDB *MongoDatabase
}

// NewMongoAdapter 创建新的MongoDB适配器
func NewMongoAdapter(uri, dbName, collectionName string) (*MongoAdapter, error) {
	mongoDB, err := NewMongoDatabase(uri, dbName, collectionName)
	if err != nil {
		return nil, err
	}
	return &MongoAdapter{mongoDB: mongoDB}, nil
}

// Close 关闭MongoDB连接
func (m *MongoAdapter) Close() error {
	return m.mongoDB.Close()
}

// SaveRecord 保存游戏记录（不存在时新插入，存在时更新）
func (m *MongoAdapter) SaveRecord(userID, gameName string, gameData interface{}) (interface{}, string, error) {
	record, action, err := m.mongoDB.SaveRecord(userID, gameName, gameData)
	if err != nil {
		return nil, "", err
	}
	return record.ID.Hex(), action, nil
}

// GetRecords 获取游戏记录
func (m *MongoAdapter) GetRecords(userID, gameName string, limit, offset int) (interface{}, int64, error) {
	return m.mongoDB.GetRecords(userID, gameName, limit, offset)
}

// DeleteRecord 删除记录（实现DatabaseInterface接口）
func (m *MongoAdapter) DeleteRecord(userID, gameName string) error {
	return m.mongoDB.DeleteRecord(userID, gameName)
}

// GetRecordByID 根据ID获取记录（额外方法）
func (m *MongoAdapter) GetRecordByID(id string) (*MongoGameRecord, error) {
	return m.mongoDB.GetRecordByID(id)
}
