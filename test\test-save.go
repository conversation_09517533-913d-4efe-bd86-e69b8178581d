package main

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"iaa-gamelog/test/common"
)

// DataSaveRequest 数据保存请求
type DataSaveRequest struct {
	AppName   string `json:"app_name"`
	Version   string `json:"version"`
	UUID      string `json:"uuid"`
	DKey      string `json:"d_key"`
	DData     string `json:"d_data"`
	Timestamp int64  `json:"timestamp"`
	Nonce     string `json:"nonce"`
	Sign      string `json:"sign"`
}

// testDataSave 测试数据保存
func testDataSave(config *common.TestConfig) error {
	common.PrintTestHeader("数据存储模块测试")

	// 测试数据
	testCases := []struct {
		name     string
		appName  string
		uuid     string
		dKey     string
		dData    string
		needSign bool
	}{
		{
			name:     "保存玩家基础数据",
			appName:  "test_game",
			uuid:     "player_001",
			dKey:     "player_basic",
			dData:    `{"level":5,"exp":1200,"coins":500}`,
			needSign: true,
		},
		{
			name:     "保存玩家装备数据",
			appName:  "test_game",
			uuid:     "player_001",
			dKey:     "player_equipment",
			dData:    `{"weapon":"sword","armor":"leather","accessories":["ring","necklace"]}`,
			needSign: true,
		},
		{
			name:     "保存游戏设置",
			appName:  "test_game",
			uuid:     "player_002",
			dKey:     "game_settings",
			dData:    `{"sound":true,"music":false,"difficulty":"normal"}`,
			needSign: true,
		},
	}

	for _, tc := range testCases {
		fmt.Printf("\n🔄 测试: %s\n", tc.name)

		// 准备请求参数
		timestamp := time.Now().Unix()
		nonce := fmt.Sprintf("test_%d", timestamp)

		// 准备签名参数（字符串格式用于签名计算）
		signParams := map[string]string{
			"app_name":  tc.appName,
			"version":   "1.0.0",
			"uuid":      tc.uuid,
			"d_key":     tc.dKey,
			"d_data":    tc.dData,
			"timestamp": strconv.FormatInt(timestamp, 10),
			"nonce":     nonce,
		}

		// 计算签名
		var sign string
		if tc.needSign {
			sign = common.CalculateSign(signParams, config.AppSecret)
		}

		// 准备请求数据（正确的数据类型）
		requestData := map[string]interface{}{
			"app_name":  tc.appName,
			"version":   "1.0.0",
			"uuid":      tc.uuid,
			"d_key":     tc.dKey,
			"d_data":    tc.dData,
			"timestamp": timestamp, // 数字类型
			"nonce":     nonce,
			"sign":      sign,
		}

		// 发送保存请求
		url := fmt.Sprintf("%s/common/game-data/s-save", config.BaseURL)
		resp, err := common.HTTPPost(config, url, requestData)
		if err != nil {
			common.PrintTestResult(tc.name+" - 保存", false, err)
			continue
		}

		if resp.Code != 0 {
			common.PrintTestResult(tc.name+" - 保存", false, fmt.Errorf("保存失败: %s", resp.Msg))
			continue
		}

		common.PrintTestResult(tc.name+" - 保存", true, nil)

		// 验证数据是否保存成功 - 获取数据
		getURL := fmt.Sprintf("%s/common/game-data/get?app_name=%s&version=1.0.0&uuid=%s&d_key=%s",
			config.BaseURL, tc.appName, tc.uuid, tc.dKey)

		getResp, err := common.HTTPGet(config, getURL)
		if err != nil {
			common.PrintTestResult(tc.name+" - 验证", false, err)
			continue
		}

		if getResp.Code != 0 {
			common.PrintTestResult(tc.name+" - 验证", false, fmt.Errorf("获取数据失败: %s", getResp.Msg))
			continue
		}

		// 检查返回的数据是否与保存的数据一致
		if getResp.Data == nil {
			common.PrintTestResult(tc.name+" - 验证", false, fmt.Errorf("返回数据为空"))
			continue
		}

		common.PrintTestResult(tc.name+" - 验证", true, nil)
		fmt.Printf("   📄 保存的数据: %s\n", tc.dData)
		fmt.Printf("   📄 获取的数据: %v\n", getResp.Data)
	}

	return nil
}

// testDataGet 测试数据获取
func testDataGet(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试数据获取功能\n")

	// 测试获取不存在的数据
	getURL := fmt.Sprintf("%s/common/game-data/get?app_name=test_game&version=1.0.0&uuid=nonexistent&d_key=nonexistent",
		config.BaseURL)

	resp, err := common.HTTPGet(config, getURL)
	if err != nil {
		common.PrintTestResult("获取不存在的数据", false, err)
		return err
	}

	if resp.Code == 0 && resp.Data == nil {
		common.PrintTestResult("获取不存在的数据", true, nil)
	} else {
		common.PrintTestResult("获取不存在的数据", false, fmt.Errorf("应该返回空数据"))
	}

	return nil
}

// testDataMultiGet 测试批量获取数据
func testDataMultiGet(config *common.TestConfig) error {
	fmt.Printf("\n🔄 测试批量数据获取功能\n")

	// 测试批量获取
	multiGetURL := fmt.Sprintf("%s/common/game-data/multi-get?app_name=test_game&version=1.0.0&uuid=player_001&d_keys=player_basic,player_equipment,nonexistent",
		config.BaseURL)

	resp, err := common.HTTPGet(config, multiGetURL)
	if err != nil {
		common.PrintTestResult("批量获取数据", false, err)
		return err
	}

	if resp.Code != 0 {
		common.PrintTestResult("批量获取数据", false, fmt.Errorf("批量获取失败: %s", resp.Msg))
		return fmt.Errorf("批量获取失败")
	}

	common.PrintTestResult("批量获取数据", true, nil)

	// 检查返回的数据结构
	if data, ok := resp.Data.(map[string]interface{}); ok {
		fmt.Printf("   📄 批量获取结果:\n")
		for key, value := range data {
			fmt.Printf("     %s: %v\n", key, value)
		}
	}

	return nil
}

func main() {
	config := common.DefaultTestConfig()

	// 检查命令行参数
	if len(os.Args) > 1 {
		if os.Args[1] == "--quiet" {
			config.Verbose = false
		}
	}

	fmt.Println("🚀 BaseNet API - 数据存储模块测试")
	fmt.Printf("服务器地址: %s\n", config.BaseURL)

	// 检查服务器状态
	if err := common.WaitForServer(config, 3); err != nil {
		fmt.Printf("❌ 服务器连接失败: %v\n", err)
		fmt.Println("请确保服务器已启动: ./gamelog.exe")
		os.Exit(1)
	}

	// 执行测试
	var hasError bool

	if err := testDataSave(config); err != nil {
		hasError = true
	}

	if err := testDataGet(config); err != nil {
		hasError = true
	}

	if err := testDataMultiGet(config); err != nil {
		hasError = true
	}

	// 输出测试总结
	fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")
	if hasError {
		fmt.Println("❌ 数据存储模块测试完成，存在失败项")
		os.Exit(1)
	} else {
		fmt.Println("✅ 数据存储模块测试全部通过")
	}
}
