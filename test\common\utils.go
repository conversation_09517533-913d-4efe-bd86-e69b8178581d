package common

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strings"
	"time"
)

const (
	BaseURL   = "http://localhost:8080"
	AppSecret = "H3qR7sL9xTmNb4cDpKjEwv8yZCiOa2fU"
)

// TestConfig 测试配置
type TestConfig struct {
	BaseURL   string
	AppSecret string
	Verbose   bool
}

// DefaultTestConfig 默认测试配置
func DefaultTestConfig() *TestConfig {
	return &TestConfig{
		BaseURL:   BaseURL,
		AppSecret: AppSecret,
		Verbose:   true,
	}
}

// StandardResponse 标准API响应格式
type StandardResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data,omitempty"`
}

// CalculateSign 计算MD5签名
func CalculateSign(params map[string]string, secret string) string {
	// 参数按字母排序
	keys := make([]string, 0, len(params))
	for k := range params {
		if k != "sign" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 拼接参数
	var paramStr strings.Builder
	for i, key := range keys {
		if i > 0 {
			paramStr.WriteString("&")
		}
		paramStr.WriteString(key)
		paramStr.WriteString("=")
		paramStr.WriteString(params[key])
	}

	// 添加密钥
	paramStr.WriteString(secret)

	// 计算MD5
	hash := md5.Sum([]byte(paramStr.String()))
	return hex.EncodeToString(hash[:])
}

// HTTPGet 发送GET请求
func HTTPGet(config *TestConfig, url string) (*StandardResponse, error) {
	if config.Verbose {
		fmt.Printf("GET %s\n", url)
	}

	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if config.Verbose {
		fmt.Printf("响应: %s\n", string(body))
	}

	var result StandardResponse
	if err := json.Unmarshal(body, &result); err != nil {
		// 如果不是标准响应格式，尝试解析为普通JSON
		var rawResult interface{}
		if err2 := json.Unmarshal(body, &rawResult); err2 != nil {
			return nil, fmt.Errorf("解析响应失败: %v", err)
		}
		return &StandardResponse{Code: 0, Msg: "success", Data: rawResult}, nil
	}

	return &result, nil
}

// HTTPPost 发送POST请求
func HTTPPost(config *TestConfig, url string, data interface{}) (*StandardResponse, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("序列化数据失败: %v", err)
	}

	if config.Verbose {
		fmt.Printf("POST %s\n", url)
		fmt.Printf("数据: %s\n", string(jsonData))
	}

	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if config.Verbose {
		fmt.Printf("响应: %s\n", string(body))
	}

	var result StandardResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &result, nil
}

// CheckHealth 检查服务健康状态
func CheckHealth(config *TestConfig) error {
	url := fmt.Sprintf("%s/health", config.BaseURL)

	if config.Verbose {
		fmt.Printf("GET %s\n", url)
	}

	resp, err := http.Get(url)
	if err != nil {
		return fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	if config.Verbose {
		fmt.Printf("响应: %s\n", string(body))
	}

	// 健康检查接口返回的是简单的JSON格式
	var healthResp map[string]interface{}
	if err := json.Unmarshal(body, &healthResp); err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}

	if status, exists := healthResp["status"]; exists && status == "ok" {
		if config.Verbose {
			fmt.Println("✅ 服务健康检查通过")
		}
		return nil
	}

	return fmt.Errorf("服务状态异常")
}

// PrintTestHeader 打印测试标题
func PrintTestHeader(title string) {
	fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")
	fmt.Printf("🧪 %s\n", title)
	fmt.Printf(strings.Repeat("=", 50) + "\n")
}

// PrintTestResult 打印测试结果
func PrintTestResult(testName string, success bool, err error) {
	if success {
		fmt.Printf("✅ %s - 通过\n", testName)
	} else {
		fmt.Printf("❌ %s - 失败: %v\n", testName, err)
	}
}

// WaitForServer 等待服务器启动
func WaitForServer(config *TestConfig, maxRetries int) error {
	fmt.Printf("等待服务器启动 (%s)...\n", config.BaseURL)

	for i := 0; i < maxRetries; i++ {
		if err := CheckHealth(config); err == nil {
			return nil
		}
		fmt.Printf("重试 %d/%d...\n", i+1, maxRetries)
		time.Sleep(2 * time.Second)
	}

	return fmt.Errorf("服务器启动超时")
}
