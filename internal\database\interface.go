package database

import (
	"iaa-gamelog/internal/models"
)

// DatabaseInterface 数据库操作接口
type DatabaseInterface interface {
	// SaveRecord 保存游戏记录（不存在时新插入，存在时更新）
	SaveRecord(userID, gameName string, gameData interface{}) (interface{}, string, error)

	// GetRecords 获取游戏记录
	GetRecords(userID, gameName string, limit, offset int) (interface{}, int64, error)

	// DeleteRecord 删除游戏记录
	DeleteRecord(userID, gameName string) error

	// Close 关闭数据库连接
	Close() error
}

// ConvertToUnified 将不同数据库的记录转换为统一格式
func ConvertToUnified(records interface{}) []models.UnifiedGameRecord {
	var unified []models.UnifiedGameRecord

	switch v := records.(type) {
	case []models.GameRecord:
		for _, record := range v {
			unified = append(unified, models.UnifiedGameRecord{
				ID:        record.ID,
				UserID:    record.UserID,
				GameName:  record.GameName,
				GameData:  record.GameData,
				CreatedAt: record.CreatedAt,
				UpdatedAt: record.UpdatedAt,
			})
		}
	case []MongoGameRecord:
		for _, record := range v {
			unified = append(unified, models.UnifiedGameRecord{
				ID:        record.ID.Hex(),
				UserID:    record.UserID,
				GameName:  record.GameName,
				GameData:  record.GameData,
				CreatedAt: record.CreatedAt,
				UpdatedAt: record.UpdatedAt,
			})
		}
	}

	return unified
}
