package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"sync"
	"time"
)

// 压力测试配置
type StressTestConfig struct {
	BaseURL        string        // 服务器地址
	Concurrency    int           // 并发数
	Duration       time.Duration // 测试持续时间
	RequestTimeout time.Duration // 请求超时时间
}

// 测试结果统计
type TestStats struct {
	mu              sync.Mutex
	TotalRequests   int64
	SuccessRequests int64
	FailedRequests  int64
	TotalLatency    time.Duration
	MinLatency      time.Duration
	MaxLatency      time.Duration
	Errors          map[string]int
}

// 游戏数据模板
var gameTemplates = []map[string]interface{}{
	{
		"score":     0,
		"level":     1,
		"play_time": 0,
		"lives":     3,
		"settings": map[string]interface{}{
			"sound":      true,
			"difficulty": "normal",
		},
	},
	{
		"score":      0,
		"level":      1,
		"play_time":  0,
		"coins":      0,
		"power_ups":  []string{},
		"checkpoint": "world-1-1",
	},
	{
		"score":         0,
		"level":         1,
		"play_time":     0,
		"lines_cleared": 0,
		"next_pieces":   []string{"T", "L", "I"},
	},
}

var gameNames = []string{"tetris", "mario", "snake", "invaders", "pacman"}

// 全局HTTP客户端，所有worker共享
var globalClient = &http.Client{
	Timeout: 10 * time.Second,
	Transport: &http.Transport{
		MaxIdleConns:        1000, // 增加最大空闲连接数
		MaxIdleConnsPerHost: 200,  // 增加每个主机的最大空闲连接数
		MaxConnsPerHost:     500,  // 限制每个主机的最大连接数
		IdleConnTimeout:     90 * time.Second,
		DisableKeepAlives:   false,
		WriteBufferSize:     32 * 1024, // 32KB写缓冲
		ReadBufferSize:      32 * 1024, // 32KB读缓冲
		// 启用HTTP/2
		ForceAttemptHTTP2: true,
	},
}

func main() {
	config := StressTestConfig{
		BaseURL:        "http://127.0.0.1:8080",
		Concurrency:    5,               // 并发数量
		Duration:       5 * time.Second, // 测试时间
		RequestTimeout: 5 * time.Second, // 超时时间
	}

	fmt.Println("=== GameLog API 压力测试 ===")
	fmt.Printf("服务器地址: %s\n", config.BaseURL)
	fmt.Printf("并发数: %d\n", config.Concurrency)
	fmt.Printf("测试时长: %v\n", config.Duration)
	fmt.Printf("请求超时: %v\n", config.RequestTimeout)
	fmt.Println()

	// 首先测试服务器连通性
	if !testConnectivity(config.BaseURL) {
		fmt.Println("❌ 服务器连接失败，请确保服务器正在运行")
		return
	}

	fmt.Println("✅ 服务器连接正常，开始压力测试...")

	// 运行压力测试
	stats := runStressTest(config)

	// 输出测试结果
	printResults(stats, config)
}

// 测试服务器连通性
func testConnectivity(baseURL string) bool {
	resp, err := globalClient.Get(baseURL + "/health")
	if err != nil {
		return false
	}
	defer resp.Body.Close()
	return resp.StatusCode == http.StatusOK
}

// 运行压力测试
func runStressTest(config StressTestConfig) *TestStats {
	stats := &TestStats{
		Errors:     make(map[string]int),
		MinLatency: time.Hour, // 初始化为一个很大的值
	}

	var wg sync.WaitGroup
	stopChan := make(chan struct{})

	// 启动定时器
	go func() {
		time.Sleep(config.Duration)
		close(stopChan)
	}()

	// 启动并发goroutine
	for i := 0; i < config.Concurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			runWorker(workerID, config, stats, stopChan)
		}(i)
	}

	// 启动实时统计显示
	go func() {
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				printRealTimeStats(stats)
			case <-stopChan:
				return
			}
		}
	}()

	wg.Wait()
	return stats
}

// 工作协程
func runWorker(workerID int, config StressTestConfig, stats *TestStats, stopChan <-chan struct{}) {
	rnd := rand.New(rand.NewSource(time.Now().UnixNano() + int64(workerID)))

	for {
		select {
		case <-stopChan:
			return
		default:
			// 随机选择测试类型
			testType := rnd.Intn(10)
			if testType < 5 { // 50% 概率测试PUT存档API
				testPutSaveWithRetry(globalClient, config.BaseURL, stats, rnd)
			} else if testType < 8 { // 30% 概率测试GET存档API
				testGetSaveWithRetry(globalClient, config.BaseURL, stats, rnd)
			} else if testType < 9 { // 10% 概率测试DELETE存档API
				testDeleteSaveWithRetry(globalClient, config.BaseURL, stats, rnd)
			} else { // 10% 概率测试健康检查
				testHealthCheckWithRetry(globalClient, config.BaseURL, stats)
			}

			// 在请求之间添加小延迟，减少连接压力
			// time.Sleep(time.Duration(rnd.Intn(50)+10) * time.Millisecond)
		}
	}
}

// 测试RESTful存档API - PUT操作（带重试）
func testPutSaveWithRetry(client *http.Client, baseURL string, stats *TestStats, rnd *rand.Rand) {
	start := time.Now()

	// 生成随机测试数据
	userID := fmt.Sprintf("user_%d", rnd.Intn(1000))
	gameName := gameNames[rnd.Intn(len(gameNames))]
	gameData := generateRandomGameData(rnd)

	jsonData, _ := json.Marshal(gameData)
	url := fmt.Sprintf("%s/api/%s/saves/%s", baseURL, gameName, userID)

	// 重试逻辑
	maxRetries := 3
	for attempt := 0; attempt <= maxRetries; attempt++ {
		req, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonData))
		if err != nil {
			latency := time.Since(start)
			updateStats(stats, err, nil, latency, "PUT /api/{gameName}/saves/{userId}")
			return
		}
		req.Header.Set("Content-Type", "application/json")

		resp, err := client.Do(req)

		// 如果成功或者是最后一次尝试，记录结果并返回
		if err == nil || attempt == maxRetries {
			latency := time.Since(start)
			updateStats(stats, err, resp, latency, "PUT /api/{gameName}/saves/{userId}")
			if resp != nil {
				resp.Body.Close()
			}
			return
		}

		// 检查是否是连接相关错误，如果是则等待后重试
		if isConnectionError(err) {
			backoffTime := time.Duration(attempt+1) * 100 * time.Millisecond
			time.Sleep(backoffTime)
			continue
		}

		// 其他错误直接返回
		latency := time.Since(start)
		updateStats(stats, err, resp, latency, "PUT /api/{gameName}/saves/{userId}")
		if resp != nil {
			resp.Body.Close()
		}
		return
	}
}

// 测试RESTful存档API - PUT操作（原版，保留兼容性）
func testPutSave(client *http.Client, baseURL string, stats *TestStats, rnd *rand.Rand) {
	testPutSaveWithRetry(client, baseURL, stats, rnd)
}

// 测试RESTful获取存档API - GET操作（带重试）
func testGetSaveWithRetry(client *http.Client, baseURL string, stats *TestStats, rnd *rand.Rand) {
	start := time.Now()

	// 生成随机测试数据
	userID := fmt.Sprintf("user_%d", rnd.Intn(1000))
	gameName := gameNames[rnd.Intn(len(gameNames))]
	url := fmt.Sprintf("%s/api/%s/saves/%s", baseURL, gameName, userID)

	// 重试逻辑
	maxRetries := 3
	for attempt := 0; attempt <= maxRetries; attempt++ {
		resp, err := client.Get(url)

		// 如果成功或者是最后一次尝试，记录结果并返回
		if err == nil || attempt == maxRetries {
			latency := time.Since(start)
			updateStats(stats, err, resp, latency, "GET /api/{gameName}/saves/{userId}")
			if resp != nil {
				resp.Body.Close()
			}
			return
		}

		// 检查是否是连接相关错误，如果是则等待后重试
		if isConnectionError(err) {
			backoffTime := time.Duration(attempt+1) * 100 * time.Millisecond
			time.Sleep(backoffTime)
			continue
		}

		// 其他错误直接返回
		latency := time.Since(start)
		updateStats(stats, err, resp, latency, "GET /api/{gameName}/saves/{userId}")
		if resp != nil {
			resp.Body.Close()
		}
		return
	}
}

// 测试RESTful获取存档API - GET操作（原版，保留兼容性）
func testGetSave(client *http.Client, baseURL string, stats *TestStats, rnd *rand.Rand) {
	testGetSaveWithRetry(client, baseURL, stats, rnd)
}

// 测试RESTful删除存档API - DELETE操作（带重试）
func testDeleteSaveWithRetry(client *http.Client, baseURL string, stats *TestStats, rnd *rand.Rand) {
	start := time.Now()

	// 生成随机测试数据
	userID := fmt.Sprintf("user_%d", rnd.Intn(1000))
	gameName := gameNames[rnd.Intn(len(gameNames))]
	url := fmt.Sprintf("%s/api/%s/saves/%s", baseURL, gameName, userID)

	// 重试逻辑
	maxRetries := 3
	for attempt := 0; attempt <= maxRetries; attempt++ {
		req, err := http.NewRequest("DELETE", url, nil)
		if err != nil {
			latency := time.Since(start)
			updateStats(stats, err, nil, latency, "DELETE /api/{gameName}/saves/{userId}")
			return
		}

		resp, err := client.Do(req)

		// 如果成功或者是最后一次尝试，记录结果并返回
		if err == nil || attempt == maxRetries {
			latency := time.Since(start)
			updateStats(stats, err, resp, latency, "DELETE /api/{gameName}/saves/{userId}")
			if resp != nil {
				resp.Body.Close()
			}
			return
		}

		// 检查是否是连接相关错误，如果是则等待后重试
		if isConnectionError(err) {
			backoffTime := time.Duration(attempt+1) * 100 * time.Millisecond
			time.Sleep(backoffTime)
			continue
		}

		// 其他错误直接返回
		latency := time.Since(start)
		updateStats(stats, err, resp, latency, "DELETE /api/{gameName}/saves/{userId}")
		if resp != nil {
			resp.Body.Close()
		}
		return
	}
}

// 测试RESTful删除存档API - DELETE操作（原版，保留兼容性）
func testDeleteSave(client *http.Client, baseURL string, stats *TestStats, rnd *rand.Rand) {
	testDeleteSaveWithRetry(client, baseURL, stats, rnd)
}

// 测试健康检查API（带重试）
func testHealthCheckWithRetry(client *http.Client, baseURL string, stats *TestStats) {
	start := time.Now()

	// 重试逻辑
	maxRetries := 3
	for attempt := 0; attempt <= maxRetries; attempt++ {
		resp, err := client.Get(baseURL + "/health")

		// 如果成功或者是最后一次尝试，记录结果并返回
		if err == nil || attempt == maxRetries {
			latency := time.Since(start)
			updateStats(stats, err, resp, latency, "GET /health")
			if resp != nil {
				resp.Body.Close()
			}
			return
		}

		// 检查是否是连接相关错误，如果是则等待后重试
		if isConnectionError(err) {
			backoffTime := time.Duration(attempt+1) * 100 * time.Millisecond
			time.Sleep(backoffTime)
			continue
		}

		// 其他错误直接返回
		latency := time.Since(start)
		updateStats(stats, err, resp, latency, "GET /health")
		if resp != nil {
			resp.Body.Close()
		}
		return
	}
}

// 测试健康检查API（原版，保留兼容性）
func testHealthCheck(client *http.Client, baseURL string, stats *TestStats) {
	testHealthCheckWithRetry(client, baseURL, stats)
}

// 判断是否是连接相关错误
func isConnectionError(err error) bool {
	if err == nil {
		return false
	}
	errorStr := err.Error()
	// 检查常见的连接错误
	return contains(errorStr, "connectex") ||
		contains(errorStr, "connection refused") ||
		contains(errorStr, "connection reset") ||
		contains(errorStr, "timeout") ||
		contains(errorStr, "network is unreachable") ||
		contains(errorStr, "socket address") ||
		contains(errorStr, "dial tcp")
}

// 字符串包含检查（不区分大小写）
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					indexOfSubstring(s, substr) >= 0))
}

// 简单的子字符串查找
func indexOfSubstring(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// 生成随机游戏数据
func generateRandomGameData(rnd *rand.Rand) map[string]interface{} {
	template := gameTemplates[rnd.Intn(len(gameTemplates))]
	gameData := make(map[string]interface{})

	// 复制模板并随机化数值
	for k, v := range template {
		switch k {
		case "score":
			gameData[k] = rnd.Intn(10000)
		case "level":
			gameData[k] = rnd.Intn(20) + 1
		case "play_time":
			gameData[k] = rnd.Intn(3600)
		case "lives":
			gameData[k] = rnd.Intn(5) + 1
		case "coins":
			gameData[k] = rnd.Intn(500)
		case "lines_cleared":
			gameData[k] = rnd.Intn(200)
		default:
			gameData[k] = v
		}
	}

	return gameData
}

// 更新统计信息
func updateStats(stats *TestStats, err error, resp *http.Response, latency time.Duration, apiEndpoint string) {
	stats.mu.Lock()
	defer stats.mu.Unlock()

	stats.TotalRequests++
	stats.TotalLatency += latency

	if latency < stats.MinLatency {
		stats.MinLatency = latency
	}
	if latency > stats.MaxLatency {
		stats.MaxLatency = latency
	}

	if err != nil {
		stats.FailedRequests++
		errorMsg := fmt.Sprintf("%s - %s", apiEndpoint, err.Error())
		stats.Errors[errorMsg]++
		return
	}

	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		stats.SuccessRequests++
	} else {
		stats.FailedRequests++
		// 读取响应内容以便调试
		var responseBody string
		if resp.Body != nil {
			body, readErr := io.ReadAll(resp.Body)
			if readErr == nil {
				responseBody = string(body)
				// 限制响应内容长度，避免日志过长
				if len(responseBody) > 200 {
					responseBody = responseBody[:200] + "..."
				}
			} else {
				responseBody = "读取响应失败"
			}
		} else {
			responseBody = "无响应内容"
		}
		errorMsg := fmt.Sprintf("%s - %d: %s", apiEndpoint, resp.StatusCode, responseBody)
		stats.Errors[errorMsg]++
	}
}

// 打印实时统计
func printRealTimeStats(stats *TestStats) {
	stats.mu.Lock()
	total := stats.TotalRequests
	success := stats.SuccessRequests
	failed := stats.FailedRequests
	avgLatency := time.Duration(0)
	if total > 0 {
		avgLatency = stats.TotalLatency / time.Duration(total)
	}
	stats.mu.Unlock()

	successRate := float64(0)
	if total > 0 {
		successRate = float64(success) / float64(total) * 100
	}

	fmt.Printf("\r实时统计 - 总请求: %d, 成功: %d, 失败: %d, 成功率: %.2f%%, 平均延迟: %v",
		total, success, failed, successRate, avgLatency)
}

// 打印最终结果
func printResults(stats *TestStats, config StressTestConfig) {
	fmt.Println("\n\n=== 压力测试结果 ===")
	fmt.Printf("测试时长: %v\n", config.Duration)
	fmt.Printf("并发数: %d\n", config.Concurrency)
	fmt.Printf("总请求数: %d\n", stats.TotalRequests)
	fmt.Printf("成功请求: %d\n", stats.SuccessRequests)
	fmt.Printf("失败请求: %d\n", stats.FailedRequests)

	successRate := float64(0)
	if stats.TotalRequests > 0 {
		successRate = float64(stats.SuccessRequests) / float64(stats.TotalRequests) * 100
	}
	fmt.Printf("成功率: %.2f%%\n", successRate)

	if stats.TotalRequests > 0 {
		avgLatency := stats.TotalLatency / time.Duration(stats.TotalRequests)
		fmt.Printf("平均延迟: %v\n", avgLatency)
		fmt.Printf("最小延迟: %v\n", stats.MinLatency)
		fmt.Printf("最大延迟: %v\n", stats.MaxLatency)

		qps := float64(stats.TotalRequests) / config.Duration.Seconds()
		fmt.Printf("QPS (每秒请求数): %.2f\n", qps)
	}

	if len(stats.Errors) > 0 {
		fmt.Println("\n错误统计:")
		for errMsg, count := range stats.Errors {
			fmt.Printf("  %s: %d次\n", errMsg, count)
		}
	}

	fmt.Println("\n=== 测试完成 ===")
}
