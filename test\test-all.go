package main

import (
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"

	"iaa-gamelog/test/common"
)

// TestModule 测试模块定义
type TestModule struct {
	Name        string
	Description string
	Command     string
	Required    bool
}

// 定义所有测试模块
var testModules = []TestModule{
	{
		Name:        "工具类模块",
		Description: "测试服务器时间、IP检查、登录码验证、健康检查等工具类接口",
		Command:     "go run test/test-utils.go --quiet",
		Required:    true,
	},
	{
		Name:        "游戏配置模块",
		Description: "测试游戏启动配置、分享配置、广告配置等接口",
		Command:     "go run test/test-config.go --quiet",
		Required:    true,
	},
	{
		Name:        "用户登录模块",
		Description: "测试微信、百度、QQ等多平台登录接口",
		Command:     "go run test/test-login.go --quiet",
		Required:    true,
	},
	{
		Name:        "数据存储模块",
		Description: "测试游戏数据保存、获取、批量获取等接口（包含签名验证）",
		Command:     "go run test/test-save.go --quiet",
		Required:    true,
	},
	{
		Name:        "统计上报模块",
		Description: "测试登录日志、游戏行为、广告统计等上报接口",
		Command:     "go run test/test-stats.go --quiet",
		Required:    true,
	},
}

// runTest 运行单个测试模块
func runTest(module TestModule) (bool, error) {
	fmt.Printf("\n🧪 运行测试: %s\n", module.Name)
	fmt.Printf("📝 描述: %s\n", module.Description)
	fmt.Printf("⚡ 命令: %s\n", module.Command)

	// 分割命令
	parts := strings.Fields(module.Command)
	if len(parts) < 2 {
		return false, fmt.Errorf("无效的测试命令")
	}

	// 执行测试命令
	cmd := exec.Command(parts[0], parts[1:]...)
	cmd.Dir = "."
	
	output, err := cmd.CombinedOutput()
	
	if err != nil {
		fmt.Printf("❌ 测试失败: %v\n", err)
		if len(output) > 0 {
			fmt.Printf("输出:\n%s\n", string(output))
		}
		return false, err
	}

	fmt.Printf("✅ 测试通过\n")
	
	// 如果需要详细输出，显示测试结果
	if len(output) > 0 {
		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			if strings.Contains(line, "✅") || strings.Contains(line, "❌") {
				fmt.Printf("   %s\n", line)
			}
		}
	}

	return true, nil
}

// checkServerStatus 检查服务器状态
func checkServerStatus() error {
	config := common.DefaultTestConfig()
	config.Verbose = false
	
	fmt.Println("🔍 检查服务器状态...")
	return common.WaitForServer(config, 5)
}

// printSummary 打印测试总结
func printSummary(results map[string]bool) {
	fmt.Printf("\n" + strings.Repeat("=", 60) + "\n")
	fmt.Printf("📊 测试总结报告\n")
	fmt.Printf(strings.Repeat("=", 60) + "\n")

	totalTests := len(results)
	passedTests := 0
	failedTests := 0

	for moduleName, passed := range results {
		if passed {
			fmt.Printf("✅ %s - 通过\n", moduleName)
			passedTests++
		} else {
			fmt.Printf("❌ %s - 失败\n", moduleName)
			failedTests++
		}
	}

	fmt.Printf(strings.Repeat("-", 60) + "\n")
	fmt.Printf("📈 统计信息:\n")
	fmt.Printf("   总测试模块: %d\n", totalTests)
	fmt.Printf("   通过模块: %d\n", passedTests)
	fmt.Printf("   失败模块: %d\n", failedTests)
	fmt.Printf("   成功率: %.1f%%\n", float64(passedTests)/float64(totalTests)*100)

	if failedTests == 0 {
		fmt.Printf("\n🎉 所有测试模块均通过！BaseNet API服务运行正常。\n")
	} else {
		fmt.Printf("\n⚠️  有 %d 个测试模块失败，请检查相关功能。\n", failedTests)
	}
}

// printUsage 打印使用说明
func printUsage() {
	fmt.Println("BaseNet API 模块化测试工具")
	fmt.Println("")
	fmt.Println("用法:")
	fmt.Println("  go run test/test-all.go [选项]")
	fmt.Println("")
	fmt.Println("选项:")
	fmt.Println("  --help, -h     显示帮助信息")
	fmt.Println("  --list, -l     列出所有可用的测试模块")
	fmt.Println("  --module, -m   运行指定的测试模块")
	fmt.Println("")
	fmt.Println("示例:")
	fmt.Println("  go run test/test-all.go                    # 运行所有测试")
	fmt.Println("  go run test/test-all.go --list             # 列出所有测试模块")
	fmt.Println("  go run test/test-all.go --module 数据存储   # 只运行数据存储模块测试")
	fmt.Println("")
	fmt.Println("单独运行测试模块:")
	fmt.Println("  go run test/test-save.go      # 数据存储模块")
	fmt.Println("  go run test/test-config.go    # 游戏配置模块")
	fmt.Println("  go run test/test-login.go     # 用户登录模块")
	fmt.Println("  go run test/test-stats.go     # 统计上报模块")
	fmt.Println("  go run test/test-utils.go     # 工具类模块")
}

// listModules 列出所有测试模块
func listModules() {
	fmt.Println("📋 可用的测试模块:")
	fmt.Println("")
	for i, module := range testModules {
		fmt.Printf("%d. %s\n", i+1, module.Name)
		fmt.Printf("   描述: %s\n", module.Description)
		fmt.Printf("   命令: %s\n", module.Command)
		fmt.Printf("   必需: %v\n", module.Required)
		fmt.Println("")
	}
}

func main() {
	// 处理命令行参数
	if len(os.Args) > 1 {
		switch os.Args[1] {
		case "--help", "-h":
			printUsage()
			return
		case "--list", "-l":
			listModules()
			return
		case "--module", "-m":
			if len(os.Args) < 3 {
				fmt.Println("❌ 请指定要运行的模块名称")
				os.Exit(1)
			}
			moduleName := os.Args[2]
			for _, module := range testModules {
				if strings.Contains(module.Name, moduleName) {
					fmt.Printf("🚀 BaseNet API 模块化测试 - %s\n", module.Name)
					if err := checkServerStatus(); err != nil {
						fmt.Printf("❌ 服务器连接失败: %v\n", err)
						os.Exit(1)
					}
					success, _ := runTest(module)
					if !success {
						os.Exit(1)
					}
					return
				}
			}
			fmt.Printf("❌ 未找到模块: %s\n", moduleName)
			os.Exit(1)
		}
	}

	// 运行所有测试
	fmt.Println("🚀 BaseNet API 全面模块化测试")
	fmt.Printf("测试时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))

	// 检查服务器状态
	if err := checkServerStatus(); err != nil {
		fmt.Printf("❌ 服务器连接失败: %v\n", err)
		fmt.Println("请确保服务器已启动: ./gamelog.exe")
		os.Exit(1)
	}

	// 运行所有测试模块
	results := make(map[string]bool)
	hasFailure := false

	for _, module := range testModules {
		success, _ := runTest(module)
		results[module.Name] = success
		
		if !success && module.Required {
			hasFailure = true
		}
		
		// 模块间稍作停顿
		time.Sleep(1 * time.Second)
	}

	// 打印总结
	printSummary(results)

	if hasFailure {
		os.Exit(1)
	}
}
